#!/usr/bin/env python3
"""
Jaeger Configuration Settings
Centralized configuration for the LLM Pattern Discovery System
"""

import os
from typing import Dict, Any

# Jaeger Version Information
JAEGER_VERSION = "2.1.0"
JAEGER_VERSION_NAME = "Production Ready Release - Zero Failures Achieved"
JAEGER_RELEASE_DATE = "2025-06-30"

# --- Robust .env loading (UNBREAKABLE RULE ENFORCED) ---
from dotenv import load_dotenv
from pathlib import Path

# Try both project root and src dir for jaeger_config.env
def _load_jaeger_env():
    root_env = Path(__file__).parent.parent / "jaeger_config.env"
    src_env = Path(__file__).parent / "jaeger_config.env"
    found = False
    for env_path in [root_env, src_env]:
        if env_path.exists():
            load_dotenv(dotenv_path=env_path, override=True)
            found = True
    if not found:
        raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: jaeger_config.env not found in project root or src directory")

_load_jaeger_env()
# --- End robust .env loading ---

class JaegerConfig:
    """Configuration class for Jaeger system"""

    def __init__(self):
        # LM Studio Configuration
        self.LM_STUDIO_URL = os.getenv('LM_STUDIO_URL', 'http://localhost:1234')
        self.LM_STUDIO_TIMEOUT = int(os.getenv('LM_STUDIO_TIMEOUT', '600'))
        self.LLM_TEMPERATURE = float(os.getenv('LLM_TEMPERATURE', '0.7'))  # CRITICAL FIX: Increased to 0.7 to generate much simpler patterns
        self.LLM_MAX_TOKENS = int(os.getenv('LLM_MAX_TOKENS', '4000'))  # Increased for complex behavioral analysis
        self.LLM_CONTEXT_LENGTH = int(os.getenv('LLM_CONTEXT_LENGTH', '64000'))  # Context window size for LLM - increased for 10 iterations
        self.LLM_MAX_LEARNING_SESSIONS = int(os.getenv('LLM_MAX_LEARNING_SESSIONS', '10'))  # Number of previous sessions to load for learning
        self.LLM_AUTO_ADJUST_CONTEXT = os.getenv('LLM_AUTO_ADJUST_CONTEXT', 'true').lower() == 'true'  # Auto-adjust context to model maximum
        
        # Model Selection Configuration
        model_selection_mode = os.getenv('LM_STUDIO_MODEL_SELECTION_MODE', 'auto')
        if model_selection_mode not in ['auto', 'manual']:
            raise ValueError('UNBREAKABLE RULE VIOLATION: LM_STUDIO_MODEL_SELECTION_MODE must be either "auto" or "manual"')
        self.LM_STUDIO_MODEL_SELECTION_MODE = model_selection_mode

        # Default Model Configuration - NO FALLBACKS ALLOWED
        self.LM_STUDIO_DEFAULT_MODEL = os.getenv('LM_STUDIO_DEFAULT_MODEL', 'meta-llama-3.1-8b-instruct')

        # Data Processing Configuration
        self.MIN_RECORDS_REQUIRED = int(os.getenv('MIN_RECORDS_REQUIRED', '100'))
        self.MAX_MEMORY_USAGE_MB = int(os.getenv('MAX_MEMORY_USAGE_MB', '2048'))
        self.DATA_VALIDATION_ENABLED = os.getenv('DATA_VALIDATION_ENABLED', 'true').lower() == 'true'

        # Trading Configuration
        stop_loss_pct = os.getenv('DEFAULT_STOP_LOSS_PCT')
        if stop_loss_pct is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_STOP_LOSS_PCT must be set in jaeger_config.env')
        self.DEFAULT_STOP_LOSS_PCT = float(stop_loss_pct)
        take_profit_pct = os.getenv('DEFAULT_TAKE_PROFIT_PCT')
        if take_profit_pct is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_TAKE_PROFIT_PCT must be set in jaeger_config.env')
        self.DEFAULT_TAKE_PROFIT_PCT = float(take_profit_pct)
        self.MAX_HOLDING_MINUTES = int(os.getenv('MAX_HOLDING_MINUTES', '180'))
        self.MIN_RISK_THRESHOLD = float(os.getenv('MIN_RISK_THRESHOLD', '1e-6'))

        # Backtesting Configuration
        self.RISK_MULTIPLE = float(os.getenv('RISK_MULTIPLE', '3.0'))
        self.MAX_R_MULTIPLE = float(os.getenv('MAX_R_MULTIPLE', '10.0'))
        self.MIN_R_MULTIPLE = float(os.getenv('MIN_R_MULTIPLE', '-10.0'))

        # File Paths - Auto-detect project root and set absolute paths
        # Get the directory where this config file is located (src directory)
        config_dir = os.path.dirname(os.path.abspath(__file__))
        # Get the project root (parent of src directory)
        project_root = os.path.dirname(config_dir)

        self.DATA_DIR = os.getenv('DATA_DIR', os.path.join(project_root, 'data'))
        self.RESULTS_DIR = os.getenv('RESULTS_DIR', os.path.join(project_root, 'results'))
        self.LOG_FILE = os.getenv('LOG_FILE', os.path.join(project_root, 'jaeger.log'))

        # MT4 Configuration
        mt4_default_lot_size = os.getenv('MT4_DEFAULT_LOT_SIZE')
        if mt4_default_lot_size is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: MT4_DEFAULT_LOT_SIZE must be set in jaeger_config.env')
        self.MT4_DEFAULT_LOT_SIZE = float(mt4_default_lot_size)
        self.MT4_MAGIC_NUMBER = int(os.getenv('MT4_MAGIC_NUMBER', '12345'))
        
        mt4_slippage = os.getenv('MT4_DEFAULT_SLIPPAGE')
        if mt4_slippage is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: MT4_DEFAULT_SLIPPAGE must be set in jaeger_config.env')
        self.MT4_SLIPPAGE = int(mt4_slippage)
        
        mt4_start_hour = os.getenv('MT4_DEFAULT_START_HOUR')
        if mt4_start_hour is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: MT4_DEFAULT_START_HOUR must be set in jaeger_config.env')
        self.MT4_DEFAULT_START_HOUR = int(mt4_start_hour)
        
        mt4_end_hour = os.getenv('MT4_DEFAULT_END_HOUR')
        if mt4_end_hour is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: MT4_DEFAULT_END_HOUR must be set in jaeger_config.env')
        self.MT4_DEFAULT_END_HOUR = mt4_end_hour  # Keep as string to handle time format like '16:30'
        
        mt4_use_time_filter = os.getenv('MT4_DEFAULT_USE_TIME_FILTER')
        if mt4_use_time_filter is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: MT4_DEFAULT_USE_TIME_FILTER must be set in jaeger_config.env')
        self.MT4_DEFAULT_USE_TIME_FILTER = mt4_use_time_filter.lower() == 'true'

        # Logging Configuration
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_TO_FILE = os.getenv('LOG_TO_FILE', 'true').lower() == 'true'
        self.LOG_TO_CONSOLE = os.getenv('LOG_TO_CONSOLE', 'true').lower() == 'true'

        # LLM Pattern Discovery Configuration (formerly "Situational Analysis")
        self.SITUATIONAL_ANALYSIS_ENABLED = os.getenv('SITUATIONAL_ANALYSIS_ENABLED', 'true').lower() == 'true'
        self.MIN_SITUATIONAL_SAMPLE_SIZE = int(os.getenv('MIN_SITUATIONAL_SAMPLE_SIZE', '20'))
        self.SITUATIONAL_SIGNIFICANCE_THRESHOLD = float(os.getenv('SITUATIONAL_SIGNIFICANCE_THRESHOLD', '0.05'))
        self.BEHAVIORAL_CONSISTENCY_THRESHOLD = float(os.getenv('BEHAVIORAL_CONSISTENCY_THRESHOLD', '0.1'))
        self.VOLATILITY_REGIME_PERIODS = int(os.getenv('VOLATILITY_REGIME_PERIODS', '20'))
        # CRITICAL: Session times are configured for UTC+1 timezone (data timezone)
        # Asian: 1:00-7:00 AM, London: 9:00-17:30, NY: 15:30-22:00 UTC+1
        self.SESSION_TRANSITION_HOURS = os.getenv('SESSION_TRANSITION_HOURS', '0,1,7,8,9,13,14,15,16,21,22').split(',')
        self.ENABLE_TOM_HOUGAARD_ANALYSIS = os.getenv('ENABLE_TOM_HOUGAARD_ANALYSIS', 'true').lower() == 'true'

        # Enhanced Backtesting and Validation Configuration
        self.ENABLE_SITUATIONAL_VALIDATION = os.getenv('ENABLE_SITUATIONAL_VALIDATION', 'true').lower() == 'true'
        self.MIN_CROSS_SITUATIONAL_SAMPLES = int(os.getenv('MIN_CROSS_SITUATIONAL_SAMPLES', '15'))
        self.BEHAVIORAL_STABILITY_PERIODS = int(os.getenv('BEHAVIORAL_STABILITY_PERIODS', '3'))
        self.CONTEXT_ADJUSTED_MIN_TRADES = int(os.getenv('CONTEXT_ADJUSTED_MIN_TRADES', '10'))
        self.VOLATILITY_ADJUSTED_EXPECTATIONS = os.getenv('VOLATILITY_ADJUSTED_EXPECTATIONS', 'true').lower() == 'true'
        self.SESSION_SPECIFIC_VALIDATION = os.getenv('SESSION_SPECIFIC_VALIDATION', 'true').lower() == 'true'
        self.PATTERN_DEGRADATION_THRESHOLD = float(os.getenv('PATTERN_DEGRADATION_THRESHOLD', '0.15'))
        self.SITUATIONAL_CONSISTENCY_THRESHOLD = float(os.getenv('SITUATIONAL_CONSISTENCY_THRESHOLD', '0.7'))

        # Walk-Forward Testing Configuration
        self.ENABLE_WALK_FORWARD_TESTING = os.getenv('ENABLE_WALK_FORWARD_TESTING', 'true').lower() == 'true'
        self.WALK_FORWARD_SPLITS = int(os.getenv('WALK_FORWARD_SPLITS', '5'))
        self.WALK_FORWARD_MIN_CONSISTENCY = float(os.getenv('WALK_FORWARD_MIN_CONSISTENCY', '60.0'))  # 60% consistency threshold
        
        # Walk-Forward Testing Default Parameters
        walkforward_cash = os.getenv('WALKFORWARD_DEFAULT_CASH')
        if walkforward_cash is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_DEFAULT_CASH must be set in jaeger_config.env')
        self.WALKFORWARD_DEFAULT_CASH = float(walkforward_cash)
        
        walkforward_commission = os.getenv('WALKFORWARD_DEFAULT_COMMISSION')
        if walkforward_commission is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_DEFAULT_COMMISSION must be set in jaeger_config.env')
        self.WALKFORWARD_DEFAULT_COMMISSION = float(walkforward_commission)
        
        walkforward_margin = os.getenv('WALKFORWARD_DEFAULT_MARGIN')
        if walkforward_margin is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_DEFAULT_MARGIN must be set in jaeger_config.env')
        self.WALKFORWARD_DEFAULT_MARGIN = float(walkforward_margin)
        
        walkforward_exclusive_orders = os.getenv('WALKFORWARD_DEFAULT_EXCLUSIVE_ORDERS')
        if walkforward_exclusive_orders is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_DEFAULT_EXCLUSIVE_ORDERS must be set in jaeger_config.env')
        self.WALKFORWARD_DEFAULT_EXCLUSIVE_ORDERS = walkforward_exclusive_orders.lower() == 'true'
        
        walkforward_n_splits = os.getenv('WALKFORWARD_DEFAULT_N_SPLITS')
        if walkforward_n_splits is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_DEFAULT_N_SPLITS must be set in jaeger_config.env')
        self.WALKFORWARD_DEFAULT_N_SPLITS = int(walkforward_n_splits)
        
        walkforward_gap = os.getenv('WALKFORWARD_DEFAULT_GAP')
        if walkforward_gap is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_DEFAULT_GAP must be set in jaeger_config.env')
        self.WALKFORWARD_DEFAULT_GAP = int(walkforward_gap)
        
        walkforward_min_multiplier = os.getenv('WALKFORWARD_MIN_MULTIPLIER')
        if walkforward_min_multiplier is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: WALKFORWARD_MIN_MULTIPLIER must be set in jaeger_config.env')
        self.WALKFORWARD_MIN_MULTIPLIER = float(walkforward_min_multiplier)

        # v2.0 Validation Configuration
        self.VALIDATION_INITIAL_CASH = float(os.getenv('VALIDATION_INITIAL_CASH', '10000'))
        self.VALIDATION_COMMISSION = float(os.getenv('VALIDATION_COMMISSION', '0.0'))
        self.VALIDATION_SPREAD = float(os.getenv('VALIDATION_SPREAD', '0.0001'))  # 1 pip
        self.VALIDATION_MARGIN = float(os.getenv('VALIDATION_MARGIN', '1.0'))
        self.VALIDATION_MIN_RETURN = float(os.getenv('VALIDATION_MIN_RETURN', '0.0'))
        self.VALIDATION_MIN_CONSISTENCY = float(os.getenv('VALIDATION_MIN_CONSISTENCY', '30.0'))
        self.VALIDATION_MIN_WIN_RATE = float(os.getenv('VALIDATION_MIN_WIN_RATE', '30.0'))
        self.VALIDATION_MAX_DRAWDOWN = float(os.getenv('VALIDATION_MAX_DRAWDOWN', '50.0'))
        self.VALIDATION_MIN_DISTANCE = float(os.getenv('VALIDATION_MIN_DISTANCE', '0.0002'))  # 2 pips minimum (more realistic for 1-min data)

        # TRUE Dynamic Pattern Discovery Configuration
        self.ENABLE_DYNAMIC_CRITERIA = os.getenv('ENABLE_DYNAMIC_CRITERIA', 'true').lower() == 'true'



        # Backtesting Execution Configuration - CENTRALIZED (ALL EDITABLE)
        # LLM provides position sizing - no hardcoded defaults
        initial_cash = os.getenv('DEFAULT_INITIAL_CASH')
        if initial_cash is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_INITIAL_CASH must be set in jaeger_config.env')
        self.DEFAULT_INITIAL_CASH = float(initial_cash)
        margin = os.getenv('DEFAULT_MARGIN')
        if margin is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_MARGIN must be set in jaeger_config.env')
        self.DEFAULT_MARGIN = float(margin)
        leverage = os.getenv('DEFAULT_LEVERAGE')
        if leverage is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_LEVERAGE must be set in jaeger_config.env')
        self.DEFAULT_LEVERAGE = float(leverage)
        spread = os.getenv('DEFAULT_SPREAD')
        if spread is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_SPREAD must be set in jaeger_config.env')
        self.DEFAULT_SPREAD = float(spread)

        # Position Sizing and Trade Limits Configuration (USER CONFIGURABLE)
        self.MAX_POSITION_SIZE_PCT = float(os.getenv('MAX_POSITION_SIZE_PCT', '1.0'))
        self.MIN_POSITION_SIZE_PCT = float(os.getenv('MIN_POSITION_SIZE_PCT', '0.5'))
        self.DEFAULT_POSITION_SIZE_PCT = float(os.getenv('DEFAULT_POSITION_SIZE_PCT', '1.0'))
        self.MAX_CONCURRENT_TRADES = int(os.getenv('MAX_CONCURRENT_TRADES', '3'))
        self.MAX_TRADES_PER_DAY = int(os.getenv('MAX_TRADES_PER_DAY', '10'))
        self.POSITION_SIZE_REDUCTION_THRESHOLD = float(os.getenv('POSITION_SIZE_REDUCTION_THRESHOLD', '2.0'))

        # Pattern Condition Thresholds Configuration (USER CONFIGURABLE)
        self.RANGE_CONTRACTION_THRESHOLD = float(os.getenv('RANGE_CONTRACTION_THRESHOLD', '0.20'))  # Reduced from 0.30 to 0.20 (20%)
        self.RANGE_EXPANSION_THRESHOLD = float(os.getenv('RANGE_EXPANSION_THRESHOLD', '1.5'))  # Reduced from 2.0 to 1.5 (150%)
        self.LOW_VOLATILITY_THRESHOLD = float(os.getenv('LOW_VOLATILITY_THRESHOLD', '0.002'))
        self.HIGH_VOLATILITY_THRESHOLD = float(os.getenv('HIGH_VOLATILITY_THRESHOLD', '0.01'))
        self.MEASURED_MOVE_THRESHOLD = float(os.getenv('MEASURED_MOVE_THRESHOLD', '1.5'))
        self.DEFAULT_LOOKBACK_PERIODS = int(os.getenv('DEFAULT_LOOKBACK_PERIODS', '20'))

        # CFD-specific Gap Thresholds (USER CONFIGURABLE)
        self.GAP_UP_THRESHOLD = float(os.getenv('GAP_UP_THRESHOLD', '0.001'))  # 0.1% for CFD trading
        self.GAP_DOWN_THRESHOLD = float(os.getenv('GAP_DOWN_THRESHOLD', '0.001'))  # 0.1% for CFD trading

        # Emergency Fallback Values (ONLY used if LLM fails to provide values)
        self.FALLBACK_RISK_REWARD_RATIO = float(os.getenv('FALLBACK_RISK_REWARD_RATIO', '2.0'))
        self.FALLBACK_STOP_LOSS_PERCENTAGE = float(os.getenv('FALLBACK_STOP_LOSS_PERCENTAGE', '2.0'))
        self.FALLBACK_TAKE_PROFIT_PERCENTAGE = float(os.getenv('FALLBACK_TAKE_PROFIT_PERCENTAGE', '6.0'))

        # LLM and Validation Configuration (USER CONFIGURABLE)
        self.LLM_TRANSLATION_TEMPERATURE = float(os.getenv('LLM_TRANSLATION_TEMPERATURE', '0.3'))
        self.VALIDATOR_MAX_RETRIES = int(os.getenv('VALIDATOR_MAX_RETRIES', '2'))
        self.VALIDATOR_RETRY_DELAY = float(os.getenv('VALIDATOR_RETRY_DELAY', '1.0'))
        self.QUALITY_SCORE_EXCELLENT_THRESHOLD = float(os.getenv('QUALITY_SCORE_EXCELLENT_THRESHOLD', '0.85'))
        self.QUALITY_SCORE_GOOD_THRESHOLD = float(os.getenv('QUALITY_SCORE_GOOD_THRESHOLD', '0.70'))
        self.QUALITY_SCORE_FAIR_THRESHOLD = float(os.getenv('QUALITY_SCORE_FAIR_THRESHOLD', '0.50'))
        commission = os.getenv('DEFAULT_COMMISSION')
        if commission is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_COMMISSION must be set in jaeger_config.env')
        self.DEFAULT_COMMISSION = float(commission)
        exclusive_orders = os.getenv('DEFAULT_EXCLUSIVE_ORDERS')
        if exclusive_orders is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_EXCLUSIVE_ORDERS must be set in jaeger_config.env')
        self.DEFAULT_EXCLUSIVE_ORDERS = exclusive_orders.lower() == 'true'
        finalize_trades = os.getenv('DEFAULT_FINALIZE_TRADES')
        if finalize_trades is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_FINALIZE_TRADES must be set in jaeger_config.env')
        self.DEFAULT_FINALIZE_TRADES = finalize_trades.lower() == 'true'
        max_holding_minutes = os.getenv('DEFAULT_MAX_HOLDING_MINUTES')
        if max_holding_minutes is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_MAX_HOLDING_MINUTES must be set in jaeger_config.env')
        self.DEFAULT_MAX_HOLDING_MINUTES = int(max_holding_minutes)
        trade_on_close = os.getenv('DEFAULT_TRADE_ON_CLOSE')
        if trade_on_close is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_TRADE_ON_CLOSE must be set in jaeger_config.env')
        self.DEFAULT_TRADE_ON_CLOSE = trade_on_close.lower() == 'true'
        hedging = os.getenv('DEFAULT_HEDGING')
        if hedging is None:
            raise ValueError('UNBREAKABLE RULE VIOLATION: DEFAULT_HEDGING must be set in jaeger_config.env')
        self.DEFAULT_HEDGING = hedging.lower() == 'true'

        # Behavioral Intelligence Configuration
        self.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = os.getenv('SIMPLIFIED_BEHAVIORAL_INTELLIGENCE', 'false').lower() == 'true'

        # ============================================================================
        # FULL LOOP AUTOMATION CONFIGURATION
        # ============================================================================
        # Parameters for automated iterative research engine

        # Full Loop Automation Settings
        self.AUTOMATED_RESEARCH_ENABLED = os.getenv('AUTOMATED_RESEARCH_ENABLED', 'false').lower() == 'true'
        self.MAX_RESEARCH_ITERATIONS = int(os.getenv('MAX_RESEARCH_ITERATIONS', '10'))
        self.RESEARCH_TIMEOUT_MINUTES = int(os.getenv('RESEARCH_TIMEOUT_MINUTES', '120'))

        # Success Criteria Thresholds
        self.MIN_SUCCESS_SHARPE_RATIO = float(os.getenv('MIN_SUCCESS_SHARPE_RATIO', '1.5'))
        self.MIN_SUCCESS_WIN_RATE = float(os.getenv('MIN_SUCCESS_WIN_RATE', '0.60'))
        self.MIN_SUCCESS_PROFIT_FACTOR = float(os.getenv('MIN_SUCCESS_PROFIT_FACTOR', '1.3'))
        self.MAX_SUCCESS_DRAWDOWN = float(os.getenv('MAX_SUCCESS_DRAWDOWN', '0.15'))
        self.MIN_STRATEGY_TRADES = int(os.getenv('MIN_STRATEGY_TRADES', '20'))

        # Decision Making Thresholds
        self.CONSECUTIVE_FAILURE_LIMIT = int(os.getenv('CONSECUTIVE_FAILURE_LIMIT', '3'))
        self.ABANDONMENT_SHARPE_THRESHOLD = float(os.getenv('ABANDONMENT_SHARPE_THRESHOLD', '0.5'))

        # Iteration Strategy Preferences
        self.ENABLE_PARAMETER_OPTIMIZATION = os.getenv('ENABLE_PARAMETER_OPTIMIZATION', 'true').lower() == 'true'
        self.ENABLE_TIMEFRAME_FOCUS = os.getenv('ENABLE_TIMEFRAME_FOCUS', 'true').lower() == 'true'
        self.ENABLE_PATTERN_SIMPLIFICATION = os.getenv('ENABLE_PATTERN_SIMPLIFICATION', 'true').lower() == 'true'

        # Smart Context Management - optimized for 10 iterations
        self.MAX_CONTEXT_ITERATIONS = int(os.getenv('MAX_CONTEXT_ITERATIONS', '5'))  # Reset every 5 iterations instead of 3
        self.CONTEXT_RESET_THRESHOLD = int(os.getenv('CONTEXT_RESET_THRESHOLD', '60000'))  # Higher threshold for 64K context
        self.LEARNING_SUMMARY_MAX_TOKENS = int(os.getenv('LEARNING_SUMMARY_MAX_TOKENS', '12000'))  # More learning preservation
        self.PRESERVE_TOP_STRATEGIES = int(os.getenv('PRESERVE_TOP_STRATEGIES', '3'))
        self.PRESERVE_RECENT_FAILURES = int(os.getenv('PRESERVE_RECENT_FAILURES', '2'))

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }

    def update_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def save_to_file(self, filepath: str = '../jaeger_config.env'):
        """Save configuration to environment file"""
        with open(filepath, 'w') as f:
            f.write("# Jaeger Configuration File\n")
            f.write("# Generated automatically - modify as needed\n\n")

            for key, value in self.to_dict().items():
                f.write(f"{key}={value}\n")

    def load_from_file(self, filepath: str = '../jaeger_config.env'):
        """Load configuration from environment file"""
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            # Convert string values to appropriate types
                            if hasattr(self, key):
                                current_value = getattr(self, key)
                                if isinstance(current_value, bool):
                                    setattr(self, key, value.lower() == 'true')
                                elif isinstance(current_value, int):
                                    setattr(self, key, int(value))
                                elif isinstance(current_value, float):
                                    setattr(self, key, float(value))
                                else:
                                    setattr(self, key, value)

# Global configuration instance
config = JaegerConfig()

# Load configuration from file if it exists (check from src directory)
if os.path.exists('../jaeger_config.env'):
    config.load_from_file()
